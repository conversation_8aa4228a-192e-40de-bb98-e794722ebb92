version: '3.8'

services:
  mysql-master:
    image: mysql:8.0
    container_name: mysql-master
    environment:
      MYSQL_ROOT_PASSWORD: 12345678
    ports:
      - "3308:3306"
    volumes:
      - mysql_master_data:/var/lib/mysql
    networks:
      - mysql-replication-net
    command:
      - --server-id=1
      - --log-bin=mysql-bin
      - --binlog-format=ROW
      - --default-authentication-plugin=mysql_native_password

  mysql-slave:
    image: mysql:8.0
    container_name: mysql-slave
    environment:
      MYSQL_ROOT_PASSWORD: 12345678
    ports:
      - "3307:3306"
    volumes:
      - mysql_slave_data:/var/lib/mysql
    networks:
      - mysql-replication-net
    depends_on:
      - mysql-master
    command:
      - --server-id=2
      - --relay-log=mysql-relay-bin
      - --read-only=1
      - --slave-skip-errors=1007,1050,1051,1060,1061
      - --default-authentication-plugin=mysql_native_password

  etcd:
    image: "bitnami/etcd:3.5"
    container_name: "etcd"
    restart: "always"
    ports:
      - 2379:2379
    environment:
      - TZ=Asia/Shanghai
      - ALLOW_NONE_AUTHENTICATION=yes
      - ETCD_ADVERTISE_CLIENT_URLS=http://etcd:2379

volumes:
  mysql_master_data:
  mysql_slave_data:

networks:
  mysql-replication-net:
    driver: bridge
