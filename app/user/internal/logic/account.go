package account

import (
	"context"
	"howToLearn/app/user/internal/logic/midware"

	"howToLearn/app/user/internal/dao"
	"howToLearn/app/user/internal/model/entity"

	"golang.org/x/crypto/bcrypt"
)

func Register(ctx context.Context, name, password, email string) (id int, err error) {
	hashPassword, err := encrypt(password)
	if err != nil {
		return 0, err
	}
	result, err := dao.Users.Ctx(ctx).Insert(entity.Users{
		Username: name,
		Password: hashPassword,
		Email:    email,
	})
	if err != nil {
		return 0, err
	}
	temp, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}
	return int(temp), nil
}

func Login(ctx context.Context, name, password string) (token string, err error) {
	user := entity.Users{}
	err = dao.Users.Ctx(ctx).Where(dao.Users.Columns().Username, name).Scan(&user)
	if err != nil {
		return "", err
	}
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return "", err
	}
	return midware.GenerateToken(user.Username)
}

// Info get user info
func Info(ctx context.Context, token string) (user *entity.Users, err error) {
	claims, err := midware.ParseToken(token)
	if err != nil {
		return nil, err
	}
	err = dao.Users.Ctx(ctx).Where(dao.Users.Columns().Username, claims.Username).Scan(&user)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func encrypt(password string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}
