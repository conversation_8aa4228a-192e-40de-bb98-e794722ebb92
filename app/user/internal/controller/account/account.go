package account

import (
	"context"
	v1 "howToLearn/app/user/api/account/v1"
	"howToLearn/app/user/api/pbentity"

	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	logic "howToLearn/app/user/internal/logic"
)

type Controller struct {
	v1.UnimplementedAccountServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterAccountServer(s.Server, &Controller{})
}

func (*Controller) UserRegister(ctx context.Context, req *v1.UserRegisterReq) (res *v1.UserRegisterRes, err error) {
	id, err := logic.Register(ctx, req.Username, req.Password, req.Email)
	if err != nil {
		return nil, err
	}
	return &v1.UserRegisterRes{
		Id: int32(id),
	}, nil
}

func (*Controller) UserLogin(ctx context.Context, req *v1.UserLoginReq) (res *v1.UserLoginRes, err error) {
	token, err := logic.Login(ctx, req.Username, req.Password)
	if err != nil {
		return nil, err
	}
	return &v1.UserLoginRes{
		Token: token,
	}, nil
}

func (*Controller) UserInfo(ctx context.Context, req *v1.UserInfoReq) (res *v1.UserInfoRes, err error) {
	user, err := logic.Info(ctx, req.Token)
	if err != nil {
		return nil, err
	}
	return &v1.UserInfoRes{
		User: &pbentity.Users{
			Id:       uint32(user.Id),
			Username: user.Username,
			Email:    user.Email,
		},
	}, nil
}
